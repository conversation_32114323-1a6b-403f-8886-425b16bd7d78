"use client";
import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import {
  DashboardLayout,
  Header,
  Sidebar,
} from '../components';
import { useChatHistory } from '../hooks/useChatHistory';
import {
  Accounticon,
  Clippingicon,
  IdeaLabicon,
  Inspirationicon,
  Keywordicon,
  SocialPosticon,
  Storyboardicon,
  Youtubeicon,
  LearningResourcesIcon,
  ProfileSettingsIcon,
  SubscriptionIcon,
  CreditsActivityIcon
} from '../components/icons/icons';

// Icon components for better consistency
const IconComponents = {
  inspiration: <Inspirationicon />,
  ideaLab: <IdeaLabicon />,
  socialPost: <SocialPosticon />,
  clipping: <Clippingicon />,
  storyboard: <Storyboardicon />,
  youtube: <Youtubeicon />,
  keyword: <Keywordicon />,
  account: <Accounticon />
};



export default function Account() {
  const [sidebarMenu, setSidebarMenu] = useState([
    { label: "Inspiration", icon: IconComponents.inspiration, active: false },
    { label: "Idea Lab", icon: IconComponents.ideaLab, active: false },
    { label: "Social Post", icon: IconComponents.socialPost, active: false },
    { label: "Clipping", icon: IconComponents.clipping, active: false },
    { label: "Storyboard Editor", icon: IconComponents.storyboard, active: false },
    { label: "YouTube Tools", icon: IconComponents.youtube, active: false },
    { label: "Keyword Insights", icon: IconComponents.keyword, active: false },
    {
      label: "Account",
      icon: IconComponents.account,
      active: true,
      submenu: [
        { label: "Learning Resources", active: true },
        { label: "Profile Settings", active: false },
        { label: "Subscription", active: false },
        { label: "Credits Activity", active: false },
      ]
    },
  ]);

  const router = useRouter();
  const [activeSubmenu, setActiveSubmenu] = useState('Learning Resources');

  // Chat history management for sidebar
  const {
    historySections,
    isLoading,
    clearAllHistory
  } = useChatHistory();

  // Handle menu item clicks with navigation
  const handleMenuItemClick = (item: any, index: number) => {
    // Handle submenu clicks for Account
    if (item.label === "Learning Resources" || item.label === "Profile Settings" || item.label === "Subscription" || item.label === "Credits Activity") {
      setActiveSubmenu(item.label);

      // Update the submenu active states
      setSidebarMenu(currentMenu => {
        const accountIndex = currentMenu.findIndex(menuItem => menuItem.label === "Account");
        if (accountIndex === -1) return currentMenu;

        const newMenuItems = [...currentMenu];
        const updatedSubmenu = newMenuItems[accountIndex].submenu?.map(subItem => ({
          ...subItem,
          active: subItem.label === item.label
        }));

        newMenuItems[accountIndex] = {
          ...newMenuItems[accountIndex],
          submenu: updatedSubmenu
        };

        return newMenuItems;
      });
      return;
    }

    const routes: { [key: string]: string } = {
      "Inspiration": "/dashboard",
      "Idea Lab": "/idealab",
      "Social Post": "/social-post",
      "Clipping": "/clipping",
      "Storyboard Editor": "/storyboard",
      "YouTube Tools": "/youtube-tools",
      "Keyword Insights": "/keyword-insights",
      "Account": "/account"
    };

    const route = routes[item.label];
    if (route) {
      router.push(route);
    }
  };

  // Handle history item click to navigate to Idea Lab
  const handleHistoryItemClick = (historyItem: any, _sectionIndex: number, _itemIndex: number) => {
    if (historyItem.sessionId) {
      // Navigate to Idea Lab with the session
      router.push('/idealab');
    }
  };

  // Handle clear history
  const handleClearHistory = () => {
    clearAllHistory();
  };

  // Render content based on active submenu
  const renderContent = () => {
    switch (activeSubmenu) {
      case 'Learning Resources':
        return (
          <div className="max-w-4xl mx-auto px-6 py-8">
            <div className="mb-8">
              <h1 className="text-2xl font-semibold text-white mb-2">Learning Resources</h1>
              <p className="text-[#9ca3af] text-sm">Access tutorials, guides, and educational content.</p>
            </div>
            <div className="text-center py-20">
              <LearningResourcesIcon />
              <h2 className="text-xl font-semibold text-white mb-2 mt-4">Learning Resources</h2>
              <p className="text-[#9ca3af]">Explore our comprehensive learning materials and tutorials.</p>
            </div>
          </div>
        );
      case 'Profile Settings':
        return (
          <div className="max-w-4xl mx-auto px-6 py-8">
            <div className="mb-8">
              <h1 className="text-2xl font-semibold text-white mb-2">Profile Settings</h1>
              <p className="text-[#9ca3af] text-sm">Manage your profile information and preferences.</p>
            </div>
            <div className="text-center py-20">
              <ProfileSettingsIcon />
              <h2 className="text-xl font-semibold text-white mb-2 mt-4">Profile Settings</h2>
              <p className="text-[#9ca3af]">Update your profile information and account preferences.</p>
            </div>
          </div>
        );
      case 'Subscription':
        return (
          <div className="max-w-4xl mx-auto px-6 py-8">
            <div className="mb-8">
              <h1 className="text-2xl font-semibold text-white mb-2">Subscription</h1>
              <p className="text-[#9ca3af] text-sm">Manage your subscription plan and billing.</p>
            </div>
            <div className="text-center py-20">
              <SubscriptionIcon />
              <h2 className="text-xl font-semibold text-white mb-2 mt-4">Subscription</h2>
              <p className="text-[#9ca3af]">View and manage your subscription details and billing information.</p>
            </div>
          </div>
        );
      case 'Credits Activity':
        return (
          <div className="max-w-4xl mx-auto px-6 py-8">
            <div className="mb-8">
              <h1 className="text-2xl font-semibold text-white mb-2">Credits Activity</h1>
              <p className="text-[#9ca3af] text-sm">Track your credit usage and transaction history.</p>
            </div>
            <div className="text-center py-20">
              <CreditsActivityIcon />
              <h2 className="text-xl font-semibold text-white mb-2 mt-4">Credits Activity</h2>
              <p className="text-[#9ca3af]">Monitor your credit balance and usage history.</p>
            </div>
          </div>
        );
      default:
        return null;
    }
  };

  return (
    <DashboardLayout
      header={<Header />}
      sidebar={
        <Sidebar
          menuItems={sidebarMenu}
          histories={historySections}
          isLoading={isLoading}
          onMenuItemClick={handleMenuItemClick}
          onHistoryItemClick={handleHistoryItemClick}
          onClearHistory={handleClearHistory}
        />
      }
    >
      <div className="flex-1 overflow-auto bg-[#0a0f0d]">
        {renderContent()}
      </div>
    </DashboardLayout>
  );
}
