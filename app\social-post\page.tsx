"use client";
import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import {
  DashboardLayout,
  Header,
  Sidebar,
} from '../components';
import { useChatHistory } from '../hooks/useChatHistory';
import {
  Inspirationicon,
  IdeaLabicon,
  SocialPosticon,
  Clippingicon,
  Storyboardicon,
  Youtubeicon,
  Keywordicon,
  Accounticon,
  NewPostIcon,
  SchedulerIcon,
  PublishedPostsIcon,
  FailedPostsIcon,
  GeneratebtnIcon
} from '../components/icons/icons';

// Icon components for better consistency
const IconComponents = {
  inspiration: <Inspirationicon />,
  ideaLab: <IdeaLabicon />,
  socialPost: <SocialPosticon />,
  clipping: <Clippingicon />,
  storyboard: <Storyboardicon />,
  youtube: <Youtubeicon />,
  keyword: <Keywordicon />,
  account: <Accounticon />
};

// Source type buttons data
const sourceTypes = [
  { id: 'text', label: 'Text', active: true },
  { id: 'article', label: 'Article', active: false },
  { id: 'youtube', label: 'YouTube', active: false },
  { id: 'tiktok', label: 'TikTok', active: false },
  { id: 'audio', label: 'Audio', active: false },
  { id: 'document', label: 'Document', active: false },
];

export default function SocialPost() {
  const [sidebarMenu, setSidebarMenu] = useState([
    { label: "Inspiration", icon: IconComponents.inspiration, active: false },
    { label: "Idea Lab", icon: IconComponents.ideaLab, active: false },
    {
      label: "Social Post",
      icon: IconComponents.socialPost,
      active: true,
      submenu: [
        { label: "New Post", active: true },
        { label: "Scheduler", active: false },
        { label: "Published Posts", active: false },
        { label: "Failed Posts", active: false },
      ]
    },
    { label: "Clipping", icon: IconComponents.clipping, active: false },
    { label: "Storyboard Editor", icon: IconComponents.storyboard, active: false },
    { label: "YouTube Tools", icon: IconComponents.youtube, active: false },
    { label: "Keyword Insights", icon: IconComponents.keyword, active: false },
    { label: "Account", icon: IconComponents.account, active: false },
  ]);

  const router = useRouter();
  const [activeSourceType, setActiveSourceType] = useState('text');
  const [sourceText, setSourceText] = useState('');
  const [activeSubmenu, setActiveSubmenu] = useState('New Post');

  // Chat history management for sidebar
  const {
    historySections,
    isLoading,
    clearAllHistory
  } = useChatHistory();

  // Handle menu item clicks with navigation
  const handleMenuItemClick = (item: any, index: number) => {
    // Handle submenu clicks for Social Post
    if (item.label === "New Post" || item.label === "Scheduler" || item.label === "Published Posts" || item.label === "Failed Posts") {
      setActiveSubmenu(item.label);

      // Update the submenu active states
      setSidebarMenu(currentMenu => {
        const socialPostIndex = currentMenu.findIndex(menuItem => menuItem.label === "Social Post");
        if (socialPostIndex === -1) return currentMenu;

        const newMenuItems = [...currentMenu];
        const updatedSubmenu = newMenuItems[socialPostIndex].submenu?.map(subItem => ({
          ...subItem,
          active: subItem.label === item.label
        }));

        newMenuItems[socialPostIndex] = {
          ...newMenuItems[socialPostIndex],
          submenu: updatedSubmenu
        };

        return newMenuItems;
      });
      return;
    }

    const routes: { [key: string]: string } = {
      "Inspiration": "/dashboard",
      "Idea Lab": "/idealab",
      "Social Post": "/social-post",
      "Clipping": "/clipping",
      "Storyboard Editor": "/storyboard",
      "YouTube Tools": "/youtube-tools",
      "Keyword Insights": "/keyword-insights",
      "Account": "/account"
    };

    const route = routes[item.label];
    if (route) {
      router.push(route);
    }
  };

  // Handle history item click to navigate to Idea Lab
  const handleHistoryItemClick = (historyItem: any, _sectionIndex: number, _itemIndex: number) => {
    if (historyItem.sessionId) {
      // Navigate to Idea Lab with the session
      router.push('/idealab');
    }
  };

  // Handle clear history
  const handleClearHistory = () => {
    clearAllHistory();
  };

  // Handle source type change
  const handleSourceTypeChange = (sourceId: string) => {
    setActiveSourceType(sourceId);
  };

  // Handle skip source
  const handleSkipSource = () => {
    // Skip source functionality
    console.log('Skip source clicked');
  };

  // Handle add source
  const handleAddSource = () => {
    // Add source functionality
    console.log('Add source clicked', { type: activeSourceType, text: sourceText });
  };

  // Render content based on active submenu
  const renderContent = () => {
    switch (activeSubmenu) {
      case 'New Post':
        return (
          <div className="max-w-4xl mx-auto px-6 py-8">
            {/* Header Section */}
            <div className="mb-8">
              <h1 className="text-2xl font-semibold text-[#887DFF] mb-2">Add source</h1>
              <p className="text-[#9ca3af] text-sm">Add your source to generate posts.</p>
            </div>

            {/* Source Type Buttons */}
            <div className="flex flex-wrap gap-3 mb-8">
              {sourceTypes.map((source) => (
                <button
                  key={source.id}
                  onClick={() => handleSourceTypeChange(source.id)}
                  className={`px-6 py-3 rounded-full text-sm font-medium transition-all duration-200 ${activeSourceType === source.id
                    ? 'bg-[#2a2a3a] text-white '
                    : 'bg-transparent text-[#9ca3af]  hover:border-[#4a4a6e] hover:text-white'
                    }`}
                >
                  {source.label}
                </button>
              ))}
            </div>

            {/* Text Input Area */}
            <div className="mb-8">
              <textarea
                value={sourceText}
                onChange={(e) => setSourceText(e.target.value)}
                placeholder="Paste the text here"
                className="w-full h-80 bg-[#1a1a2e] border border-[#2a2a3e] rounded-xl p-6 text-white text-base resize-none focus:outline-none focus:border-[#4a4a6e] placeholder-[#6b7280] transition-colors"
                style={{
                  fontFamily: 'var(--font-geist-sans), -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif'
                }}
              />
            </div>

            {/* Action Buttons */}
            <div className="flex justify-end gap-4">
              <button
                onClick={handleSkipSource}
                className="px-6 py-3 rounded-full text-white text-sm font-medium border border-[#4a4a6e] bg-transparent hover:bg-[#1a1a2e] transition-colors"
              >
                Skip Source
              </button>
              <button
                onClick={handleAddSource}
                className="px-6 py-3 rounded-full text-white text-sm font-medium bg-[#887DFF] hover:bg-[#5855eb] transition-colors flex items-center gap-2"
              >
                <GeneratebtnIcon />
                Add
              </button>
            </div>
          </div>
        );
      case 'Scheduler':
        return (
          <div className="max-w-4xl mx-auto px-6 py-8">
            <div className="text-center py-20">
              <SchedulerIcon />
              <h2 className="text-xl font-semibold text-white mb-2 mt-4">Scheduler</h2>
              <p className="text-[#9ca3af]">Schedule your posts for optimal engagement.</p>
            </div>
          </div>
        );
      case 'Published Posts':
        return (
          <div className="max-w-4xl mx-auto px-6 py-8">
            <div className="text-center py-20">
              <PublishedPostsIcon />
              <h2 className="text-xl font-semibold text-white mb-2 mt-4">Published Posts</h2>
              <p className="text-[#9ca3af]">View your successfully published posts.</p>
            </div>
          </div>
        );
      case 'Failed Posts':
        return (
          <div className="max-w-4xl mx-auto px-6 py-8">
            <div className="text-center py-20">
              <FailedPostsIcon />
              <h2 className="text-xl font-semibold text-white mb-2 mt-4">Failed Posts</h2>
              <p className="text-[#9ca3af]">Review posts that failed to publish.</p>
            </div>
          </div>
        );
      default:
        return null;
    }
  };

  return (
    <DashboardLayout
      header={<Header />}
      sidebar={
        <Sidebar
          menuItems={sidebarMenu}
          histories={historySections}
          isLoading={isLoading}
          onMenuItemClick={handleMenuItemClick}
          onHistoryItemClick={handleHistoryItemClick}
          onClearHistory={handleClearHistory}
        />
      }
    >
      <div className="flex-1 overflow-auto bg-[#0a0f0d]">
        {renderContent()}
      </div>
    </DashboardLayout>
  );
}
