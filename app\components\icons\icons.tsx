import React from "react";


//AUTH ICONS

//google icon
export const Googleicon = () => (
  <svg className="w-5 h-5" viewBox="0 0 24 24">
    <path
      fill="#4285F4"
      d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"
    />
    <path
      fill="#34A853"
      d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"
    />
    <path
      fill="#FBBC05"
      d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"
    />
    <path
      fill="#EA4335"
      d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"
    />
  </svg>
);

{/* SVG for Apple Icon */ }
export const AppleIcon = () => (
  <svg className="w-5 h-5" viewBox="0 0 24 24" fill="currentColor">
    <path d="M12.152 6.896c-.948 0-2.415-1.078-3.96-1.04-2.04.027-3.91 1.183-4.961 3.014-2.117 3.675-.546 9.103 1.519 12.09 1.013 1.454 2.208 3.09 3.792 3.039 1.52-.065 2.09-.987 3.935-.987 1.831 0 2.35.987 3.96.948 1.637-.026 2.676-1.48 3.676-2.948 1.156-1.688 1.636-3.325 1.662-3.415-.039-.013-3.182-1.221-3.22-4.857-.026-3.04 2.48-4.494 2.597-4.559-1.429-2.09-3.623-2.324-4.39-2.376-2-.156-3.675 1.09-4.61 1.09zM15.53 3.83c.843-1.012 1.4-2.427 1.245-3.83-1.207.052-2.662.805-3.532 1.818-.78.896-1.454 2.338-1.273 3.714 1.338.104 2.715-.688 3.559-1.701" />
  </svg>
);




// SIDEBAR ICONS

export const Inspirationicon = () => (<svg width="20" height="21" viewBox="0 0 20 21" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M7.66347 15.8916H12.3364M9.99995 1.8916V2.8916M16.3639 4.52764L15.6568 5.23475M19 10.8915H18M2 10.8915H1M4.34309 5.23475L3.63599 4.52764M6.46441 14.4272C4.51179 12.4745 4.51179 9.30871 6.46441 7.35609C8.41703 5.40347 11.5829 5.40347 13.5355 7.35609C15.4881 9.30871 15.4881 12.4745 13.5355 14.4272L12.9884 14.9743C12.3555 15.6071 11.9999 16.4655 11.9999 17.3606V17.8916C11.9999 18.9962 11.1045 19.8916 9.99995 19.8916C8.89538 19.8916 7.99995 18.9962 7.99995 17.8916V17.3606C7.99995 16.4655 7.6444 15.6071 7.01151 14.9743L6.46441 14.4272Z" stroke="#6D6D6D" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" /></svg>)
export const IdeaLabicon = () => (<svg width="20" height="21" viewBox="0 0 20 21" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M3 1.8916V5.8916M1 3.8916H5M4 15.8916V19.8916M2 17.8916H6M11 1.8916L13.2857 8.74874L19 10.8916L13.2857 13.0345L11 19.8916L8.71429 13.0345L3 10.8916L8.71429 8.74874L11 1.8916Z" stroke="#6D6D6D" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" /></svg>)
export const SocialPosticon = () => (<svg width="20" height="19" viewBox="0 0 20 19" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M2.31802 3.20962C0.56066 4.96698 0.56066 7.81622 2.31802 9.57358L10.0001 17.2556L17.682 9.57358C19.4393 7.81622 19.4393 4.96698 17.682 3.20962C15.9246 1.45226 13.0754 1.45226 11.318 3.20962L10.0001 4.52769L8.68198 3.20962C6.92462 1.45226 4.07538 1.45226 2.31802 3.20962Z" stroke="#6D6D6D" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" /></svg>)
export const Clippingicon = () => (<svg width="17" height="19" viewBox="0 0 17 19" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M11.1213 12.0129L16 16.8916M9 9.8916L16 2.8916M9 9.8916L6.12132 12.7703M9 9.8916L6.12132 7.01292M6.12132 12.7703C5.57843 12.2274 4.82843 11.8916 4 11.8916C2.34315 11.8916 1 13.2347 1 14.8916C1 16.5485 2.34315 17.8916 4 17.8916C5.65685 17.8916 7 16.5485 7 14.8916C7 14.0632 6.66421 13.3132 6.12132 12.7703ZM6.12132 7.01292C6.66421 6.47003 7 5.72003 7 4.8916C7 3.23475 5.65685 1.8916 4 1.8916C2.34315 1.8916 1 3.23475 1 4.8916C1 6.54846 2.34315 7.8916 4 7.8916C4.82843 7.8916 5.57843 7.55582 6.12132 7.01292Z" stroke="#6D6D6D" strokeWidth="2" strokeLinecap="round" /></svg>)
export const Storyboardicon = () => (<svg width="20" height="21" viewBox="0 0 20 21" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M13.2322 4.12383L16.7677 7.65937M14.7322 2.62383C15.7085 1.64752 17.2914 1.64752 18.2677 2.62383C19.244 3.60015 19.244 5.18306 18.2677 6.15937L4.5 19.9271H1V16.356L14.7322 2.62383Z" stroke="#6D6D6D" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" /></svg>)
export const Youtubeicon = () => (<svg width="20" height="21" viewBox="0 0 20 21" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M12.7519 10.0596L9.5547 7.92807C8.89015 7.48503 8 7.96142 8 8.76012V13.0231C8 13.8218 8.89015 14.2982 9.5547 13.8551L12.7519 11.7237C13.3457 11.3278 13.3457 10.4554 12.7519 10.0596Z" stroke="#6D6D6D" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" /><path d="M19 10.8916C19 15.8622 14.9706 19.8916 10 19.8916C5.02944 19.8916 1 15.8622 1 10.8916C1 5.92104 5.02944 1.8916 10 1.8916C14.9706 1.8916 19 5.92104 19 10.8916Z" stroke="#6D6D6D" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" /></svg>)
export const Keywordicon = () => (<svg width="16" height="21" viewBox="0 0 16 21" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M6 19.8916H13C14.1046 19.8916 15 18.9962 15 17.8916V8.30582C15 8.0406 14.8946 7.78624 14.7071 7.59871L9.29289 2.18449C9.10536 1.99696 8.851 1.8916 8.58579 1.8916H3C1.89543 1.8916 1 2.78703 1 3.8916V14.8916M1 19.8916L5.87868 15.0129M5.87868 15.0129C6.42157 15.5558 7.17157 15.8916 8 15.8916C9.65685 15.8916 11 14.5485 11 12.8916C11 11.2347 9.65685 9.8916 8 9.8916C6.34315 9.8916 5 11.2347 5 12.8916C5 13.72 5.33579 14.47 5.87868 15.0129Z" stroke="#5D5D5D" strokeWidth="2" strokeLinecap="round" /></svg>)
export const Accounticon = () => (<svg width="16" height="21" viewBox="0 0 16 21" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M12 5.89111C12 8.10025 10.2091 9.89111 8 9.89111C5.79086 9.89111 4 8.10025 4 5.89111C4 3.68197 5.79086 1.89111 8 1.89111C10.2091 1.89111 12 3.68197 12 5.89111Z" stroke="#F7F5F2" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" /><path d="M8 12.8911C4.13401 12.8911 1 16.0251 1 19.8911H15C15 16.0251 11.866 12.8911 8 12.8911Z" stroke="#F7F5F2" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" /></svg>)

//IDEALAB ICONS
export const NewIdeaIcon = () => (
  <svg width="15" height="15" viewBox="0 0 15 15" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M6.25 2.64112H2.5C1.67157 2.64112 1 3.31269 1 4.14112V12.3911C1 13.2195 1.67157 13.8911 2.5 13.8911H10.75C11.5784 13.8911 12.25 13.2195 12.25 12.3911V8.64112M11.1893 1.58045C11.7751 0.994667 12.7249 0.994667 13.3107 1.58045C13.8964 2.16624 13.8964 3.11599 13.3107 3.70177L6.87132 10.1411H4.75L4.75 8.01979L11.1893 1.58045Z" stroke="white" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
  </svg>);

// SOCIAL POST SUBMENU ICONS
export const NewPostIcon = () => (<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
  <path d="M9 13H15M12 10V16M3 17V7C3 5.89543 3.89543 5 5 5H11L13 7H19C20.1046 7 21 7.89543 21 9V17C21 18.1046 20.1046 19 19 19H5C3.89543 19 3 18.1046 3 17Z" stroke="#F7F5F2" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
</svg>
);


export const SchedulerIcon = () => (<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
  <path d="M12 8V12L15 15M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z" stroke="#6D6D6D" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
</svg>

);

export const PublishedPostsIcon = () => (<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
  <path d="M12 10V16M12 16L9 13M12 16L15 13M3 17V7C3 5.89543 3.89543 5 5 5H11L13 7H19C20.1046 7 21 7.89543 21 9V17C21 18.1046 20.1046 19 19 19H5C3.89543 19 3 18.1046 3 17Z" stroke="#6D6D6D" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
</svg>

);

export const FailedPostsIcon = () => (<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
  <path d="M12 9.00001V11M12 15H12.01M20.6179 5.98434C20.4132 5.99472 20.2072 5.99997 20 5.99997C16.9265 5.99997 14.123 4.84453 11.9999 2.94434C9.87691 4.84446 7.07339 5.99985 4 5.99985C3.79277 5.99985 3.58678 5.9946 3.38213 5.98422C3.1327 6.94783 3 7.95842 3 9.00001C3 14.5915 6.82432 19.2898 12 20.622C17.1757 19.2898 21 14.5915 21 9.00001C21 7.95847 20.8673 6.94791 20.6179 5.98434Z" stroke="#6D6D6D" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
</svg>
);

// Account SUBMENU ICONS
export const LearningResourcesIcon = () => (<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
  <path d="M9 13H15M12 10V16M3 17V7C3 5.89543 3.89543 5 5 5H11L13 7H19C20.1046 7 21 7.89543 21 9V17C21 18.1046 20.1046 19 19 19H5C3.89543 19 3 18.1046 3 17Z" stroke="#F7F5F2" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
</svg>
);


export const ProfileSettingsIcon = () => (<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
  <path d="M12 8V12L15 15M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z" stroke="#6D6D6D" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
</svg>

);

export const SubscriptionIcon = () => (<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
  <path d="M12 10V16M12 16L9 13M12 16L15 13M3 17V7C3 5.89543 3.89543 5 5 5H11L13 7H19C20.1046 7 21 7.89543 21 9V17C21 18.1046 20.1046 19 19 19H5C3.89543 19 3 18.1046 3 17Z" stroke="#6D6D6D" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
</svg>

);

export const CreditsActivityIcon = () => (<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
  <path d="M12 9.00001V11M12 15H12.01M20.6179 5.98434C20.4132 5.99472 20.2072 5.99997 20 5.99997C16.9265 5.99997 14.123 4.84453 11.9999 2.94434C9.87691 4.84446 7.07339 5.99985 4 5.99985C3.79277 5.99985 3.58678 5.9946 3.38213 5.98422C3.1327 6.94783 3 7.95842 3 9.00001C3 14.5915 6.82432 19.2898 12 20.622C17.1757 19.2898 21 14.5915 21 9.00001C21 7.95847 20.8673 6.94791 20.6179 5.98434Z" stroke="#6D6D6D" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
</svg>
);


// SVG for music gen 
export const Musicgeneratoricon = () => (
  <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M7.69784 6.9355C7.76123 7.67067 7.81057 8.42485 7.81057 8.61498C7.81057 9.26776 7.99371 9.4769 8.45855 9.35648C8.86 9.25508 8.89517 7.82911 8.54301 6.28273C8.52188 6.18132 8.6064 6.08626 8.71909 6.08626L8.91635 6.0926C9.36006 6.09893 9.52204 6.10527 9.50091 5.85177C9.49387 5.75036 9.35297 5.07857 9.17689 2.80336C9.04307 1.06685 8.85293 0.515472 8.67686 1.35838C8.62051 1.62456 8.698 5.17364 8.76139 5.44616C8.76843 5.47785 8.75434 5.51587 8.72617 5.53488C8.698 5.5539 8.65574 5.56023 8.62053 5.54756C7.69084 5.20533 7.54994 5.23702 7.69784 6.9355Z" fill="#887DFF" />
    <path d="M5.46516 5.81223C5.96522 5.64745 5.97929 5.61576 5.768 5.10242C5.64827 4.81722 5.13412 2.96663 5.13412 2.81453C5.13412 2.78918 5.25384 2.8779 5.40175 3.01733C5.88068 3.4673 6.36662 3.35956 6.92303 2.68144C7.10615 2.45962 7.36675 2.21879 7.50057 2.15541C7.76821 2.02232 7.79637 1.97162 7.62733 1.91458C7.38787 1.83219 6.66243 2.14274 6.21872 2.52933L5.99339 2.71946C5.19048 1.97796 4.37346 1.53432 4.48615 2.1871C4.51432 2.36455 4.58475 2.66876 4.69744 3.20112C4.92282 4.24683 4.92983 4.1898 4.56359 4.16445C3.80294 4.11374 2.80282 5.36226 3.29584 5.74886C3.64799 6.04673 4.68338 6.07208 5.46516 5.81223ZM4.11989 4.91862C4.33118 4.4433 4.88055 4.37359 5.03549 4.79821C5.30313 5.52704 5.24677 5.61576 4.52838 5.61576C3.9931 5.6221 3.88747 5.44465 4.11989 4.91862Z" fill="#887DFF" />
    <path d="M5.33163 11.2367C5.3598 11.091 4.30337 10.153 2.96518 9.13265L2.19044 8.54325C2.09184 8.4672 2.12 8.32143 2.24677 8.28974L2.46508 8.22637C3.16939 8.01089 3.19055 8.09327 2.17634 6.88912L1.43682 6.00185C0.429656 4.7977 0 4.44279 0 4.81671C0 4.9498 0.697252 5.97016 1.42973 6.90813C2.01431 7.65598 2.18334 7.90314 2.09883 7.90314C2.09178 7.90314 1.8171 7.94751 1.50016 7.99187C0.563433 8.13764 0.443728 8.25805 0.964916 8.5179C1.33116 8.70169 3.12011 10.077 3.93711 10.8121C4.68368 11.4839 5.24007 11.655 5.33163 11.2367Z" fill="#887DFF" />
    <path d="M23.7491 7.56882C23.3829 6.92238 21.4953 5.19854 20.798 4.86899C19.5796 4.29226 19.2909 4.22255 18.34 4.22889C18.0231 4.23522 17.4667 4.28592 17.0864 4.3493C15.389 4.64083 14.3325 5.15418 12.445 6.6055C11.2406 7.5308 9.21217 10.1102 9.21217 10.7123C9.21217 10.9214 9.02905 10.9341 8.43743 10.7757C7.00064 10.3891 5.8949 10.9531 3.92284 13.0889C2.54944 14.5719 2.81709 14.6987 3.09177 15.0472L2.88048 15.3831C1.97192 16.8345 2.36635 20.4913 6.20483 20.9476C7.04295 21.049 8.39523 20.8779 8.81077 20.618C9.05728 20.4659 9.11357 20.4976 9.17695 20.8272C9.65588 23.5143 15.3044 23.8249 17.7695 21.3025C18.1076 20.9539 18.1992 20.8969 18.3048 20.9476C18.7063 21.1377 19.0866 20.8018 20.1642 19.2998C21.4319 17.5379 21.4249 16.8028 20.1501 15.3641L19.6641 14.8191L20.2698 14.2931C20.7558 14.5656 20.8121 14.686 21.3052 14.0015C21.4812 13.7607 21.7348 13.4565 21.8616 13.3361C21.9884 13.2157 22.101 13.0889 22.101 13.0699C22.101 13.0445 22.3194 12.7594 22.587 12.4361C23.8055 10.9721 24.3619 8.63988 23.7491 7.56882ZM11.6632 8.25963C12.1844 7.5815 13.6916 6.22524 14.3184 5.85766C16.4947 4.59647 18.488 4.24156 19.5726 4.76125C21.6644 5.76893 21.6503 7.02378 19.5444 10.237C19.3261 10.5665 19.1359 10.8834 19.1077 10.9468C19.0795 11.0228 19.0021 11.0609 18.8612 11.0609C18.6288 11.0609 18.3823 11.0292 18.5372 10.839C20.6924 8.0695 21.1573 6.51044 19.5937 5.54078C17.8259 4.44437 14.6283 5.74358 12.1421 8.43074C11.6139 9.00113 11.3956 8.6082 11.6632 8.25963ZM16.6286 12.7087C16.7202 12.791 16.6497 12.8417 16.4243 13.0445C15.0227 12.696 14.7269 12.4551 13.2338 13.3361C12.9662 13.4945 12.938 13.5072 12.5717 13.0192C12.4238 12.8227 12.5084 12.4044 12.7056 12.4044C12.8676 12.4044 12.8183 12.2333 12.5999 12.0115C12.3253 11.7327 12.4449 11.5108 12.8041 11.6439C13.3394 11.8404 13.4944 11.7327 14.3536 10.5855C15.5862 8.93142 17.4174 7.57516 19.0444 7.10617C19.3895 7.01111 19.9952 6.9921 19.9952 7.08082C19.9952 7.34067 19.2768 8.81734 18.8753 9.39406C18.7274 9.60321 18.5231 9.90741 18.4175 10.0785C18.1992 10.4271 16.8399 12.0812 16.6638 12.208C16.5229 12.303 16.5018 12.5946 16.6286 12.7087ZM12.9873 10.3447C13.262 10.6046 13.4029 10.5475 13.3536 10.6869C13.3395 10.7186 13.1141 11.232 12.9943 11.1496C12.8323 11.0355 11.8111 10.3003 11.6491 10.218C11.5083 10.1482 11.5646 10.1166 12.0576 9.55884C12.2689 9.72996 12.8112 10.1799 12.9873 10.3447ZM12.452 9.10887L13.093 8.39272C15.1143 6.14919 17.5864 4.95771 18.9316 5.58514C19.3824 5.79428 19.8191 6.25059 19.9529 6.66254C19.9951 6.78295 19.96 6.78929 19.305 6.78929C17.8541 6.78929 15.4031 8.24695 13.924 9.9898L13.7338 10.2116C12.9802 9.51448 12.952 9.48913 12.452 9.10887ZM18.4809 11.4158L18.8753 11.4538L18.5725 11.9671C18.178 12.6389 17.995 12.5249 17.3329 12.3918C18.1076 11.4284 18.164 11.4158 18.4809 11.4158ZM10.9025 9.29266C11.2406 8.90606 11.6914 9.11521 11.4167 9.52082C11.2547 9.75531 11.0011 9.72362 10.9025 9.69827C10.7264 9.65391 10.649 9.57786 10.9025 9.29266ZM10.9377 10.313C11.0927 10.3891 11.3955 10.5475 11.6209 10.7186C12.2548 11.213 12.1844 11.2066 12.0365 11.3841L11.959 11.4791C11.9308 11.5108 11.8745 11.5235 11.8393 11.4982C11.5646 11.3144 10.5645 10.6553 10.5363 10.6362C10.487 10.5855 10.6912 10.1926 10.9377 10.313ZM9.26148 11.5299C9.28966 11.4728 9.45868 11.3461 9.63475 11.3524L10.332 11.6186C10.3884 11.6376 10.4518 11.6249 10.494 11.5869C10.5856 11.4918 10.656 11.4094 10.6983 11.3461C10.8039 11.1623 11.9026 11.8657 11.9026 12.1193C11.9026 12.2016 11.8533 12.303 11.797 12.3411C11.6139 12.4805 11.8111 12.5756 12.1632 12.8924C12.3534 13.0636 12.3534 13.3171 12.3252 13.3551C12.2971 13.3931 12.0999 13.3551 11.9661 13.2283C11.5012 12.7974 10.6912 12.1953 9.47278 11.7263C9.31079 11.6629 9.2474 11.5679 9.26148 11.5299ZM3.48615 14.2424C3.96508 13.767 6.12027 11.5425 6.65554 11.2827C7.19082 11.0292 7.90216 11.0482 8.29657 11.0482C8.88819 11.0545 8.88117 11.3968 8.3952 11.4158C7.87401 11.4284 7.52187 11.3841 6.93025 11.701C6.46541 11.9481 4.45814 14.0966 3.84539 14.5846C3.66227 14.7177 3.31712 14.4071 3.48615 14.2424ZM9.02908 19.3252C8.99386 19.7878 8.96566 19.8829 8.86001 19.9146C8.78958 19.9336 8.53603 20.0667 8.29657 20.2124C5.38073 21.9679 2.42969 18.121 3.80309 15.6303C4.42289 14.5085 5.62724 13.5959 6.21886 12.8734C6.93725 12.0052 7.51481 11.6693 8.32477 11.758C8.86004 11.815 9.71929 12.1573 10.6701 12.7847C12.1562 13.7607 12.3112 13.9635 11.6632 14.5276C11.2547 14.8825 10.8744 15.25 10.5786 15.5352C10.4729 15.6366 10.4658 15.7824 10.5504 15.8965L10.7687 16.1753L10.5363 16.3972C9.69115 17.1957 9.09951 18.3492 9.02908 19.3252ZM17.1004 21.2264C12.9168 24.7502 7.81768 21.2835 10.3109 17.5569C10.6842 16.9992 10.98 16.6317 11.1209 16.4669C11.1702 16.4035 11.2617 16.3845 11.3392 16.4098C11.4026 16.4288 11.473 16.4542 11.5294 16.4669C11.8041 16.5556 11.8252 16.4986 11.3322 17.0943C9.8813 18.8562 10.4377 20.6877 12.283 21.4229C13.8113 22.025 15.2622 21.9743 16.6426 20.8208C17.0793 20.4532 17.185 20.2821 17.4456 20.4469C17.6921 20.599 17.2554 21.0934 17.1004 21.2264ZM14.0156 15.4592C14.0719 15.5986 13.9944 15.6557 13.8747 15.7127C13.4732 15.9028 12.4943 16.7521 12.3041 16.9866C11.3885 18.121 11.297 17.9942 11.8815 16.9295C12.3252 16.0993 13.8606 15.0663 14.0156 15.4592ZM14.079 14.6226C14.5579 14.2614 15.2622 14.0776 15.6566 14.217C15.72 14.236 15.8257 14.2867 15.8257 14.3438C15.8257 14.4832 16.0933 14.686 16.3469 14.8508C16.4666 14.9268 16.375 15.0029 16.2905 14.9775C15.3467 14.686 14.8185 14.6416 14.2057 14.8127C13.6493 14.9649 13.6423 14.9522 14.079 14.6226ZM16.9666 19.4392C16.0651 21.2771 13.4662 21.9616 12.121 20.7194C11.6562 20.2885 11.466 19.5533 11.5928 19.097C12.0224 17.5696 15.0861 14.9268 16.213 15.4592C18.6218 16.5873 17.347 18.6597 16.9666 19.4392ZM17.3681 15.6113C17.0653 15.3768 17.023 15.2057 17.9104 15.5796C18.4034 15.7887 18.664 16.1056 18.7203 16.0866C19.0936 15.9599 19.4176 16.4162 19.4317 16.9422C19.4458 17.4492 19.157 18.0196 18.7274 18.5329C18.2485 19.1097 18.0443 19.04 18.3189 18.5393C18.826 17.6013 18.4246 16.4098 17.3681 15.6113ZM20.4671 16.7077C20.5093 17.1323 20.453 18.1147 19.7276 19.0716C19.4177 19.4709 19.0092 20.0286 18.8472 20.2441C18.664 20.4849 18.4879 20.5483 18.2766 20.4913C18.0653 20.4342 17.4103 20.0857 17.6569 19.8258C17.847 19.623 18.2484 19.7244 18.4386 19.7878C18.495 19.8068 18.5513 19.7878 18.5866 19.7434C19.4247 18.4886 20.7418 17.3985 19.3754 15.8711C19.2557 15.738 19.157 15.605 19.157 15.5733C19.157 15.4655 18.8612 15.4402 18.7485 15.5352C18.6429 15.624 18.5654 15.5986 17.8893 15.2247C17.4808 14.9965 17.0018 14.743 16.8187 14.6543C16.5792 14.5402 16.4806 14.4578 16.4806 14.3564C16.4806 14.1917 16.2201 13.9255 16.0581 13.9255C15.9947 13.9255 15.8116 13.8811 15.6637 13.8241C15.2974 13.6846 14.4804 13.672 14.079 13.9762C13.586 14.3438 12.3956 15.3261 11.9238 15.6873C11.8956 15.7064 11.8886 15.7444 11.9097 15.7761C12.1281 16.0613 11.8745 16.2134 11.5224 16.0106L11.3603 15.8775C11.2476 15.7824 11.2476 15.6176 11.3603 15.5226C11.9026 15.0726 13.2972 13.9191 13.9099 13.4185C14.0719 13.2854 14.2832 13.2283 14.5016 13.2537C15.1073 13.3234 16.2271 13.4818 16.5229 13.5262C16.7976 13.5642 16.8399 13.5516 17.1075 13.3044C17.192 13.2283 17.2554 13.1523 17.2977 13.1016C17.3329 13.0572 17.3962 13.0382 17.4526 13.0445C17.4526 13.0445 18.0302 13.0826 18.2344 13.1586C18.3894 13.2157 19.1148 13.7353 19.4387 13.9889C19.5233 14.0586 19.5303 14.179 19.4458 14.2487L18.8964 14.7113C18.8401 14.8444 18.9598 14.9395 19.1852 14.9395C19.4669 14.9395 20.4248 16.2577 20.4671 16.7077ZM20.8685 13.6023C20.4107 14.1029 20.4811 14.0966 19.9177 13.6339C19.5444 13.3297 19.143 13.0762 18.8683 12.8988C19.0162 12.7213 19.1781 12.5439 19.3894 12.1636C19.6218 11.7453 19.5866 11.739 20.2839 12.2967C20.9671 12.8417 21.108 12.9875 21.277 13.1523L20.8685 13.6023ZM21.6855 12.6516C21.439 12.4361 21.2206 12.2207 20.5304 11.7897C19.812 11.3397 19.8191 11.4348 20.3896 10.5855C21.5728 8.82368 22.0095 7.5308 21.77 6.56747L21.6996 6.28228C22.6223 7.1442 23.2491 7.69558 23.3195 7.96176C23.6224 9.15957 23.0378 10.2623 21.6855 12.6516Z" fill="#887DFF" />
    <path d="M8.32452 13.753C8.5499 13.6516 9.14149 13.8925 9.14149 14.0066C9.14149 14.3678 10.5149 14.8558 10.8107 14.6467C11.3249 14.2854 8.74003 13.132 7.90895 13.3538C6.72571 13.6707 5.12696 16.2818 5.47207 17.3338C5.78901 18.3035 7.72587 18.8232 8.93728 18.2655C9.47256 18.0183 9.09223 17.8091 8.40905 17.9739C7.10608 18.2908 6.16229 17.657 6.28202 16.5479C6.35246 15.8762 7.69064 14.0446 8.32452 13.753Z" fill="#887DFF" />
  </svg>
);

{/* SVG for Sound Effects */ }
export const SoundEffect = () => (<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
  <path d="M2.29909 16.8197C3.60977 16.3194 3.19644 15.8286 1.83535 16.2713C1.08927 16.5118 1.02875 16.5695 1.20015 16.8293C1.36146 17.0795 1.6135 17.0795 2.29909 16.8197Z" fill="#887DFF" />
  <path d="M1.93638 17.2714C1.38187 17.4639 1.28105 17.5794 1.5331 17.7622C1.79523 17.9546 2.32957 17.8873 2.82359 17.6082C3.51926 17.2041 2.84378 16.9539 1.93638 17.2714Z" fill="#887DFF" />
  <path d="M21.8686 11.9591C21.3444 11.5935 21.2435 11.4203 21.4553 11.2952C21.5863 11.2182 22.4433 9.52469 23.0382 8.17759C23.7943 6.46485 23.8548 6.51298 20.9814 6.46487L18.9347 6.42636L19.1263 6.07035C20.7798 2.97202 20.1749 1.55757 17.7652 2.89504C17.1603 3.23182 16.4243 3.63594 16.1319 3.78028C15.5068 4.10743 15.527 4.19404 15.779 2.5679C16.0109 1.05723 15.5472 0.595349 14.4885 1.27852C14.297 1.40361 13.6215 1.90396 12.9964 2.38507C11.6655 3.41463 11.7764 3.37616 11.4941 2.85656C10.9195 1.827 10.0625 1.46135 9.70958 2.10603C9.25588 2.93354 8.66107 5.94527 8.93328 6.03187C9.25591 6.13771 9.99192 5.02153 10.4053 3.79952C10.4759 3.60707 10.8892 4.03045 11.0203 4.42495C11.1816 4.91568 11.4337 4.80984 13.0165 3.60707C13.6114 3.14521 14.1256 2.77958 14.1357 2.77958C14.1558 2.77958 14.1054 3.09711 14.0248 3.47237C13.5005 6.03186 14.1054 5.73358 16.4646 4.60779C18.5919 3.58784 18.3903 3.55898 17.9568 4.84834L17.4124 6.48411C17.0595 7.54254 17.4124 7.99478 18.3097 7.63876C18.7129 7.47519 21.6569 7.5329 21.6569 7.69648C21.6569 7.73497 21.3746 8.2642 21.0419 8.86077C20.1648 10.4099 20.0236 10.8718 19.7514 11.2182C19.4893 11.5357 19.6909 12.19 20.1043 12.267C20.5378 12.344 21.1125 12.9213 22.2014 13.9124C18.9751 15.1248 18.9045 14.9516 20.5983 17.1647C21.0721 17.7901 21.4553 18.3097 21.4351 18.3289C21.415 18.3386 20.7395 18.3097 19.9228 18.252C17.856 18.1077 17.6745 18.2135 18.1786 19.1949C18.4911 19.8011 18.481 19.8011 17.7249 19.3778C16.1521 18.4829 14.9422 18.9736 16.4546 19.8974C20.1043 22.1393 20.568 22.1971 20.1043 20.3881C19.7615 19.0795 19.7716 19.0602 20.1749 19.1083C20.7294 19.1757 22.282 19.2142 22.7155 19.3585C24.0464 19.8108 23.7943 18.8678 21.9997 16.7413C21.4855 16.1255 21.0621 15.5866 21.0621 15.5385C21.0621 15.4231 23.2499 14.4801 23.7036 14.4031C24.0565 14.3454 24.0867 14.0952 23.8346 13.7199C23.5322 13.2677 22.8567 12.6423 21.8686 11.9591Z" fill="#887DFF" />
  <path d="M9.89095 18.8307C12.0485 18.9269 13.8935 19.6582 16.0209 18.4362C18.6523 16.9351 16.5754 9.93981 14.9017 7.69786C13.7927 6.21605 12.2502 5.41742 10.2841 6.4085C9.09445 7.00507 8.85249 7.19751 7.37042 8.76592C6.66467 9.50683 5.85807 10.267 5.57577 10.4402C5.29347 10.623 4.95072 10.8443 4.80957 10.9309C1.43206 13.1536 0.675878 13.7213 0.403661 14.2602C-0.342417 15.7324 -0.0298905 17.6279 1.13964 18.6671C1.45218 18.9461 3.99288 20.5723 4.11386 20.5723C4.15419 20.5723 4.35587 20.7936 4.55752 21.063C6.05975 23.0644 7.7737 23.5551 9.37675 22.4582C10.5261 21.6788 10.4757 21.0534 9.17511 19.8602L8.77183 19.4946L9.1852 18.7826L9.89095 18.8307ZM0.867449 15.6458C1.24049 14.4334 3.10569 12.6918 2.94438 13.7021C2.74273 14.9626 3.90214 17.2527 5.17249 18.1283L5.69678 18.4939L5.32372 18.7345C3.42828 20.0046 0.222192 17.7722 0.867449 15.6458ZM8.83233 21.2939C8.53995 21.5056 8.27781 21.1015 8.27781 21.1015C7.84428 20.6877 7.44097 20.8513 7.74343 21.2747C7.95515 21.5634 8.06608 21.5538 7.76361 21.8617C7.33008 22.2947 7.13852 22.2177 6.10006 21.2747C4.46676 19.7833 4.36596 19.562 5.16245 19.2541C5.55565 19.1001 5.56572 19.0231 5.80769 19.4369C6.24122 20.1778 6.76548 20.0527 7.81402 19.9372C7.95517 19.918 8.08625 19.9757 8.17699 20.0719L8.45927 20.3991C8.68108 20.6011 9.05414 21.1304 8.83233 21.2939ZM7.80393 18.6575C8.16689 18.9173 7.45105 19.4369 7.1385 19.4369C6.92678 19.4369 6.42271 19.0905 6.42271 18.9461C6.42271 18.7537 7.55188 18.4747 7.80393 18.6575ZM5.4145 17.1949C3.13594 15.232 3.09561 12.9997 5.31368 11.7103C6.66468 10.9213 7.84429 9.98794 8.69119 9.03535C9.00374 8.68895 9.37676 8.14049 9.58849 7.97691C9.9212 7.70749 9.97162 7.94805 9.80022 8.21747C7.70314 11.4024 8.8525 15.2031 10.3547 16.7715C12.3208 18.821 15.5773 18.0609 15.5773 15.7612C15.5773 15.2416 15.3454 15.1358 15.174 15.5976C14.0649 18.6863 12.0586 16.6849 12.0485 16.6849C12.3006 15.8574 12.4215 15.1743 13.6415 14.6162C15.8091 13.6347 13.9439 10.4979 11.625 11.2196C10.6773 11.5179 9.61873 11.0464 9.52799 10.9501C9.52799 10.9405 9.73969 8.46764 11.4738 7.46693C11.9678 7.17827 11.5948 6.79339 11.0201 7.06281C10.637 7.24563 10.4757 7.02431 10.6269 6.86074C11.0403 6.41812 11.736 6.29303 12.1897 6.47585C16.1721 8.07312 18.733 18.4939 13.8935 18.4073C13.2483 18.3977 11.8872 18.2052 9.89095 17.9839C8.02576 17.7915 6.80583 18.3977 5.4145 17.1949ZM9.74981 12.3935C9.74981 12.2684 9.87078 12.2107 10.3346 12.1625C10.6572 12.1241 10.9798 12.0567 11.0605 12.0182C11.4436 11.8065 11.5343 11.9124 11.5948 12.6148C11.6654 13.4038 11.9578 14.0581 12.3208 14.2409C12.4619 14.3083 12.5728 14.4045 12.5728 14.443C12.5728 14.52 11.0706 15.5784 10.9496 15.5784C10.758 15.588 9.74981 12.9034 9.74981 12.3935ZM13.0063 11.8642C13.1576 12.0856 13.4802 13.2113 13.4802 13.5192C13.4802 13.7887 13.2584 13.5385 12.966 12.9419C12.5426 12.1048 12.5829 11.2484 13.0063 11.8642Z" fill="#887DFF" />
</svg>);

// svg for text to image 
export const TexttoImageIcon = () => (
  <svg width="25" height="24" viewBox="0 0 25 24" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path fillRule="evenodd" clipRule="evenodd" d="M8.00347 1.14911C7.53495 1.47565 7.86683 2.40084 8.60865 2.85437C9.23335 3.23533 11.9078 3.36232 13.86 3.12648C19.2089 2.45526 22.1371 3.81585 21.8443 6.80912C21.4343 11.1992 21.3562 14.3014 21.6491 15.7889C21.9809 17.5123 21.9419 17.3853 21.3172 16.9499C20.7121 16.5327 19.326 15.6801 18.5451 15.2265C17.9009 14.8637 16.7882 14.3376 16.5734 15.0089C16.222 16.1155 15.1679 17.8207 14.2308 18.9817C12.103 21.6122 8.00347 22.2108 4.48957 21.6485C3.00593 21.4126 2.96689 20.941 3.02545 19.9069C3.22067 16.2062 3.49393 6.33745 3.96245 4.25123C4.19671 3.19905 4.47003 3.01764 5.34851 2.7818C6.89071 2.3827 5.83658 1.69334 4.02107 1.89289C2.75217 2.03802 2.02987 2.4734 1.58087 4.01539C0.897613 6.42815 0.9757 10.8183 0.507181 19.7981C0.390051 21.9024 1.71752 22.6281 3.63064 22.8095C4.76289 22.9184 6.48078 22.9546 9.36998 22.9909C12.962 23.0272 14.7579 23.0816 15.9682 22.9183C17.3348 22.7369 17.94 22.2834 19.5603 21.2857C24.675 18.0928 24.4797 18.3831 24.4992 14.1744C24.5383 4.52334 23.0937 2.05616 17.1982 1.80219C15.4803 1.72963 13.8209 1.54821 13.1962 1.38494C11.5564 0.967699 8.47198 0.822573 8.00347 1.14911ZM11.4783 6.30116C10.0532 6.41001 9.56523 7.38963 10.7756 7.69802C11.7712 7.952 16.7687 8.00642 18.428 7.84315C19.4431 7.7343 19.0331 7.06309 18.2132 6.73655C17.0029 6.24674 14.5236 6.06533 11.4783 6.30116ZM9.01863 10.0019C5.66091 10.1471 6.53936 10.9997 8.02301 11.2718C11.0293 11.816 19.0136 11.8886 19.0136 11.163C19.0136 10.1652 15.1875 9.72982 9.01863 10.0019ZM9.03807 16.0792C10.4241 16.0429 13.1963 15.8252 13.7038 15.6801C14.5823 15.4261 13.3719 14.5553 11.8493 14.3558C9.79949 14.0837 8.94046 14.0111 8.45242 14.0474C5.87557 14.1744 7.00782 16.1336 9.03807 16.0792ZM21.6295 18.111C21.3953 18.2742 19.1503 20.3786 16.4563 21.4308C15.4412 21.8299 14.9141 21.7755 15.5778 20.9773C16.1049 20.0883 17.9205 16.3332 18.3695 16.3332C18.6232 16.3513 21.6295 18.111 21.6295 18.111Z" fill="#887DFF" />
  </svg>);

{/* SVG for Image to Image */ }

export const ImagetoImageicon = () => (
  <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M23.6927 14.6443C22.2927 7.25807 20.0457 3.25584 16.5888 1.90224C15.7073 1.54903 13.9617 0.430825 9.1221 1.28423C4.61088 2.10824 1.13678 10.4067 0.341709 13.7319C-1.404 20.8828 3.47012 23.3254 16.5543 23.0016C17.6951 22.5896 25.6458 24.9438 23.6927 14.6443ZM11.6282 2.75564C12.3542 2.54964 14.1519 2.25544 15.9322 3.78565C16.0185 3.84445 16.0703 3.99165 16.0877 4.13865C16.105 4.28585 16.0704 4.43306 16.0185 4.55065C14.7914 6.72827 13.2704 7.05207 12.6309 7.11087C12.4407 7.11087 12.2505 6.96367 12.1468 6.72827C11.8875 6.16927 11.4728 5.02145 11.3344 3.52064C11.2999 3.13825 11.4382 2.81444 11.6282 2.75564ZM12.0431 9.43569C11.8184 10.4949 9.24309 15.4681 8.89737 15.1739C8.55177 14.8797 9.27763 9.46509 9.77888 8.49388C10.2801 7.49347 12.2679 8.37628 12.0431 9.43569ZM6.73687 4.52125C7.34181 3.34425 8.75911 2.93224 9.57142 2.84384C9.64061 2.84384 9.69242 2.87324 9.72695 2.96164C9.76161 3.04984 9.77888 3.13825 9.76161 3.25584C9.22582 6.02207 6.85789 6.87547 6.68505 6.81667C6.49492 6.75766 6.2702 5.78666 6.73687 4.52125ZM4.78378 9.55328C4.36897 11.4367 4.14423 14.0263 4.14423 16.7629C4.14423 19.4704 4.12696 19.4997 2.95164 18.9111C1.62075 18.2637 1.2923 16.9689 1.94911 15.0267L2.32939 13.8497L2.58866 15.4093C3.15903 18.7935 3.71214 16.8513 3.28003 13.0257C3.10718 11.4955 3.19364 11.1129 4.17884 9.28849C5.00848 7.78767 5.16404 7.84647 4.78378 9.55328ZM5.6307 14.6149C5.38872 14.4383 5.85541 10.3773 6.25294 9.20029C6.65048 8.02308 8.13686 7.43447 8.27512 8.22908C8.43065 9.05308 5.87268 14.8207 5.6307 14.6149ZM22.1371 18.5581C22.1026 19.0877 21.1519 20.0294 20.0457 20.5886C19.0259 21.1182 9.48508 21.03 8.39612 20.4708L7.89499 20.2354L9.53689 17.1455C12.6654 11.2601 15.3963 7.49348 17.1073 6.75767C18.4383 6.16927 18.3692 6.34587 20.2531 9.93589C18.905 10.9069 19.0432 10.9953 18.1098 10.2007C16.6753 8.96489 16.1913 9.55328 16.8655 11.7015C17.1938 12.7609 17.2457 12.7021 14.9642 14.3499C12.5789 16.0861 11.2481 17.4103 11.2481 18.0871C11.2481 18.9995 11.6629 18.8523 13.495 17.3221C17.6778 13.7909 18.542 14.2029 17.9889 12.4373C17.747 11.7015 17.9889 11.7015 18.7321 12.4667C19.7173 13.4965 20.0111 12.9963 21.0654 11.8781C21.5494 13.8791 22.2581 16.4393 22.1371 18.5581Z" fill="#887DFF" />
  </svg>);

{/* SVG for Text to Video */ }

export const TexttoVideoicon = () => (<svg width="25" height="24" viewBox="0 0 25 24" fill="none" xmlns="http://www.w3.org/2000/svg">
  <path d="M10.3286 17.2818C10.4569 17.0229 13.6647 15.1068 15.3896 14.2611C18.4692 12.7419 18.7828 12.4658 18.5974 11.4474C18.3551 10.1183 15.8744 8.56468 11.6116 7.06292C9.1167 6.18256 8.57499 6.09625 7.97618 6.45874C7.7053 6.6141 7.61973 6.83845 7.59122 7.30462C7.59122 7.32185 7.17777 14.8134 7.02094 14.9514C6.15129 15.711 10.0434 17.8342 10.3286 17.2818ZM9.14528 15.1414C9.04541 15.0205 9.30209 8.13312 9.40184 8.13312C9.45889 8.13312 15.9458 10.5152 15.9458 10.8949C15.9601 11.2574 9.33056 15.3657 9.14528 15.1414Z" fill="#887DFF" />
  <path d="M24.2148 7.52955C23.0886 2.5064 21.9621 1.62605 16.0742 1.21177C12.6382 0.970114 7.16375 0.883804 5.92341 1.07369C4.78287 1.2463 3.25734 2.07485 2.87241 2.73079C2.72985 2.97246 2.38768 3.50758 2.08828 3.93912C1.23288 5.18196 1.63213 6.02778 3.14334 6.21765C3.55679 6.26943 3.95596 5.97598 4.11277 5.50993C4.2696 5.07838 4.42646 4.57779 4.56902 4.11173C4.83989 3.19686 5.31029 2.83436 6.47933 2.62723C9.65864 2.07486 16.3307 2.85163 17.3858 2.88615C20.8074 3.04151 21.392 3.24864 21.6059 4.40519C22.8747 11.4825 22.447 14.7277 22.1476 15.9531C20.9642 20.8383 18.7972 20.7347 6.26549 20.7175C5.58116 20.7175 4.93967 20.3723 4.46919 19.7853C2.17386 16.8681 1.87441 12.173 2.77258 8.72053C2.94367 8.04742 1.61778 7.13246 1.20434 7.63309C0.434477 8.56528 -0.477985 17.7829 3.10046 20.8727C5.09641 22.599 16.9296 23.4102 19.1822 22.8579C20.7932 22.4782 23.174 19.8889 23.8013 17.8865C24.4999 15.5562 24.7423 9.87718 24.2148 7.52955Z" fill="#887DFF" />
</svg>
);

{/* SVG for Image to Video */ }
export const ImagetoVideoicon = () => (<svg width="25" height="24" viewBox="0 0 25 24" fill="none" xmlns="http://www.w3.org/2000/svg">
  <path fillRule="evenodd" clipRule="evenodd" d="M9.30981 1.06896C6.80171 1.27123 6.2758 1.49372 6.9635 2.06007C7.14554 2.22188 8.88507 2.28256 12.6674 2.24211C19.4434 2.18143 20.5153 2.30279 21.3446 3.19276C22.2144 4.12318 23.873 4.40635 23.873 3.61752C23.873 3.25344 22.4369 1.93871 21.7492 1.67576C20.3738 1.14987 12.809 0.785793 9.30981 1.06896ZM2.45301 2.62642C-1.34959 6.42901 0.875317 22.9339 5.06222 22.0035C5.85106 21.8215 5.83085 21.5383 4.98134 21.1944C3.34298 20.5067 2.9182 19.1718 3.34296 16.1175C3.58568 14.3781 3.62611 12.9217 3.52498 9.03824C3.3834 3.71865 3.52502 2.97026 4.67794 2.58596C5.34541 2.38369 4.98134 1.99939 4.07114 1.91848C3.32275 1.8578 3.14072 1.93871 2.45301 2.62642ZM16.7128 4.93225C15.5194 5.88289 16.1868 7.13694 17.9061 7.13694C19.1399 7.13694 19.8276 6.55037 19.8276 5.49859C19.8276 4.46703 17.7848 4.08273 16.7128 4.93225ZM22.1132 5.35701C21.5266 7.05604 21.5671 8.02692 21.5671 17.6143C21.5065 17.7964 22.1132 19.9202 19.4837 20.4865C18.4522 20.709 16.6115 20.7697 12.6876 20.8101C6.11395 20.9113 5.79032 20.9517 6.51848 21.8012C7.67139 23.1969 19.6455 23.5003 22.1941 22.2058C23.8122 21.3765 24.5 18.0391 24.5 10.9395C24.4595 6.69196 22.9223 3.01072 22.1132 5.35701ZM8.19734 9.03824C4.79928 12.2138 8.03547 17.7964 13.8001 16.8659C17.4206 16.2794 18.6747 8.73484 15.0946 8.73484C14.3867 8.73484 14.2855 9.03824 14.8519 9.48323C17.7645 11.7688 14.6091 16.5423 11.0897 15.4096C7.48937 14.2567 8.29848 8.5528 12.2427 8.5528C13.1326 8.5528 13.6787 8.14828 12.7887 7.7842C12.0201 7.46057 10.3009 7.09649 8.19734 9.03824Z" fill="#887DFF" />
</svg>
);

{/* SVG for Text to Speech */ }
export const TexttoSpeechicon = () => (<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
  <path fillRule="evenodd" clipRule="evenodd" d="M7.3925 1.04634C7.1409 1.16103 7.11196 1.25279 7.1797 1.66568C7.88612 5.78321 7.34421 17.8376 7.12164 22.0124C7.10228 22.3794 7.28605 22.7235 7.57636 22.8726C8.21504 23.1823 8.96016 23.0103 8.88274 22.563C8.61179 20.9916 8.70866 11.0247 9.04735 5.51941C9.26024 2.04418 9.24089 1.57393 8.82478 1.26426C8.44738 0.988989 7.74087 0.885764 7.3925 1.04634ZM11.4763 4.32659C11.3698 4.48716 11.3795 5.0721 11.5246 6.79251C11.815 10.2448 11.8923 13.1351 11.7374 14.5573C11.4084 17.5279 11.3988 17.2985 11.8246 17.5623C12.6859 18.0899 13.2664 17.8146 13.0438 16.9774C12.7922 16.0025 12.8407 10.1531 13.1213 7.98533C13.3923 5.85202 13.3342 4.69361 12.9181 4.34953C12.5794 4.06279 11.6698 4.05132 11.4763 4.32659ZM3.46366 5.53088C3.80235 8.96023 3.9088 10.9444 3.80235 12.1717C3.49269 15.7157 3.4056 16.5989 3.2798 17.2641C3.11529 18.1587 3.28948 18.3766 4.18943 18.3766C4.97326 18.3766 4.93456 18.6289 4.77005 14.3164C4.63457 10.5774 4.64434 10.0957 4.99271 5.57676C5.11851 3.8334 3.27012 3.63842 3.46366 5.53088ZM18.6177 4.45275C18.4242 4.62479 18.3953 4.78536 18.4533 5.45059C18.792 9.41901 18.8211 10.9903 18.5985 13.3989C18.434 15.1078 18.3855 15.9795 18.3661 16.5645C18.3468 17.0806 18.6953 17.5164 19.1307 17.5394C19.5662 17.5623 19.9339 17.1494 19.9533 16.6333C19.9726 16.0827 19.9145 15.2799 19.8081 14.1215C19.363 9.37313 20.5532 5.55382 20.0016 4.6592C19.7791 4.30365 18.9177 4.17749 18.6177 4.45275ZM22.4015 6.86133C22.0725 7.0219 21.9951 7.62978 22.2177 8.4097C22.4499 9.24697 22.5079 11.9423 22.3337 13.7544C22.2176 14.9587 22.4111 15.2799 23.253 15.2799C24.0755 15.2799 24.2208 14.477 23.6789 12.9631C23.4369 12.2864 23.4465 10.004 23.6885 8.91436C23.9981 7.48068 23.3305 6.41402 22.4015 6.86133ZM15.3083 7.64125C15.5793 8.76525 15.5986 12.4928 15.3374 13.8577L15.1632 14.7752C15.1245 15.0046 15.1922 15.234 15.3567 15.3602C15.6373 15.5896 16.276 15.6469 16.5083 15.4634C16.7695 15.2569 16.7985 14.6147 16.6243 13.0089C16.4888 11.7588 16.4696 11.0935 16.5663 10.1416C16.8663 7.21688 16.8663 7.42333 16.547 7.14806C16.4696 7.09072 14.9987 6.34521 15.3083 7.64125ZM0.260674 8.21472C-0.0973726 9.04052 0.376797 11.495 0.0671353 14.1329C-0.107049 15.624 0.0380944 16.0254 0.734834 16.0254C1.32513 16.0254 1.38318 15.796 1.30576 13.8921C1.23802 12.2634 1.26706 11.5409 1.4993 9.24697C1.63478 7.95092 0.705813 7.18247 0.260674 8.21472Z" fill="#887DFF" />
</svg>
);

{/* SVG for Lipsync */ }

export const LipSyncIcon = () => (<svg width="37" height="14" viewBox="0 0 37 14" fill="none" xmlns="http://www.w3.org/2000/svg">
  <path d="M1.35088 7.34875C5.02625 7.9014 7.49767 7.36206 10.8513 7.51116C14.7954 7.6865 13.7036 5.75641 17.8736 7.3151C19.1973 7.80993 21.5225 8.06232 22.4804 6.93908C22.849 6.50687 26.5357 7.19511 27.3695 7.19511C30.0702 7.19511 32.599 7.90763 35.3088 7.1571C35.4065 7.13001 35.5411 7.14391 35.6493 7.14391M1 7.02945V7.30022C1 8.85235 9.45594 11.8173 11.6513 12.3001C14.331 12.8893 22.6536 12.8927 25.3487 12.3001C27.5469 11.8167 36 8.85183 36 7.30022C36 6.44418 35.7331 6.30698 34.7715 5.94526C34.1717 5.71964 33.6046 5.55261 32.9183 5.36688C29.6902 4.4933 27.126 3.73809 24.1432 2.2773C23.7839 2.10131 23.699 2.01283 23.3161 1.89317C21.0579 1.1872 20.6353 2.60732 18.3109 2.60732C16.2668 2.60732 15.7602 0.820088 12.9195 2.24677C12.38 2.51774 11.8531 2.75615 11.2805 3.02952C10.1694 3.55996 9.02478 3.88725 7.77535 4.28808C6.21501 4.78869 3.6996 5.39689 2.29594 5.91927C1.74813 6.1232 1 6.35908 1 7.02945Z" stroke="#887DFF" strokeWidth="2" strokeMiterlimit="2.61313" />
</svg>
);


//generate icon
export const GeneratebtnIcon = () => (<svg width="16" height="16" viewBox="0 0 20 21" fill="none" xmlns="http://www.w3.org/2000/svg">
  <path d="M3 1.8916V5.8916M1 3.8916H5M4 15.8916V19.8916M2 17.8916H6M11 1.8916L13.2857 8.74874L19 10.8916L13.2857 13.0345L11 19.8916L8.71429 13.0345L3 10.8916L8.71429 8.74874L11 1.8916Z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
</svg>
);


// COMMON ICONS
// Select options down arrow
export const SelectArrow = () => (
  <svg className="absolute right-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400 pointer-events-none" fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
  </svg>);

//Download icon
export const DownloadIcon = () => (
  <svg width="39" height="39" viewBox="0 0 39 39" fill="none" xmlns="http://www.w3.org/2000/svg">
    <rect x="0.82251" y="0.977539" width="37.3509" height="37.3509" rx="3.39554" fill="white" fillOpacity="0.2" />
    <path d="M12.7068 23.0488L12.7068 23.8977C12.7068 25.3042 13.847 26.4443 15.2534 26.4443L23.7423 26.4443C25.1488 26.4443 26.2889 25.3042 26.2889 23.8977L26.2889 23.0488M22.8934 19.6533L19.4979 23.0488M19.4979 23.0488L16.1023 19.6533M19.4979 23.0488L19.4979 12.8622" stroke="#F7F5F2" strokeWidth="1.69777" strokeLinecap="round" strokeLinejoin="round" />
  </svg>);

//Image Download Icon
export const ImageDownloadIcon = () => (<svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
  <path d="M21 15v4a2 2 0 01-2 2H5a2 2 0 01-2-2v-4M7 10l5 5 5-5M12 15V3" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
</svg>
);

// circular add btn
export const CircularaddbtnIcon = () => (
  <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M12 5v14M5 12h14" stroke="white" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
  </svg>);

//share button audio

export const ShareIcon = () => (
  <svg width="14" height="14" fill="none" viewBox="0 0 24 24">
    <circle cx="18" cy="5" r="3" stroke="#fff" strokeWidth="1.5" opacity="0.8" />
    <circle cx="6" cy="12" r="3" stroke="#fff" strokeWidth="1.5" opacity="0.8" />
    <circle cx="18" cy="19" r="3" stroke="#fff" strokeWidth="1.5" opacity="0.8" />
    <line x1="9" y1="10.5" x2="15" y2="6.5" stroke="#fff" strokeWidth="1.5" opacity="0.8" />
    <line x1="9" y1="13.5" x2="15" y2="17.5" stroke="#fff" strokeWidth="1.5" opacity="0.8" />
  </svg>
);

//download icon audio
export const DownloadIconaudio = () => (
  <svg width="14" height="14" fill="none" viewBox="0 0 24 24">
    <path d="M4 16v1a3 3 0 0 0 3 3h10a3 3 0 0 0 3-3v-1M12 4v12m0 0l-4-4m4 4l4-4" stroke="#fff" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" opacity="0.8" />
  </svg>
);

//Generating loading icon
export const GeneratingLoadingIcon = () => (<svg width="43" height="20" viewBox="0 0 43 20" fill="none" xmlns="http://www.w3.org/2000/svg">
  <path fillRule="evenodd" clipRule="evenodd" d="M13.347 19.3695C12.2262 19.3799 11.0653 19.3683 10.5679 19.325C10.4166 19.3223 10.2837 19.2126 10.2574 19.0579C9.33142 13.6504 5.95901 13.1841 2.21669 12.6668C1.71185 12.597 1.20044 12.5262 0.677224 12.4414C0.512572 12.4273 0.383301 12.2893 0.383301 12.121V7.65607H0.384674C0.384674 7.47917 0.528231 7.33564 0.70513 7.33564C1.6495 7.33224 2.70522 7.25921 3.71944 7.06167C4.53227 6.90337 5.31333 6.66522 5.97942 6.31936C5.99008 6.31324 6.00097 6.30757 6.01253 6.30258C9.49131 4.7885 9.80815 2.10305 9.91995 1.15437C9.9508 0.892651 9.97053 0.725067 10.076 0.615072C10.0996 0.591032 10.1272 0.569925 10.159 0.553369C10.3178 0.470136 13.7968 0.485791 15.3446 0.492595C15.5038 0.493276 15.637 0.493924 16.0078 0.493924C16.1538 0.494151 16.2863 0.594409 16.3207 0.742732C16.6952 2.34661 15.3385 5.54167 13.589 7.54857C13.0561 8.16 12.4834 8.66759 11.908 8.99508C11.2977 9.34253 10.6797 9.49108 10.0916 9.35795C9.49541 9.22301 8.95088 8.80476 8.505 8.01779L8.1952 9.1665C8.16957 9.26062 8.10177 9.34229 8.00402 9.38129L6.7571 9.88002L7.982 10.3663C8.07907 10.4007 8.158 10.481 8.18567 10.588L8.47415 11.6941C8.57825 11.4102 8.67917 11.16 8.79438 10.9437C9.30898 9.97845 9.96667 9.77612 11.7543 10.7277L11.7645 10.733C13.105 11.4628 14.239 12.589 15.0348 13.9144C15.8243 15.2291 16.2829 16.7414 16.2829 18.2591C16.2829 18.3636 16.2799 18.4841 16.2743 18.6197C16.2688 18.7544 16.2613 18.8746 16.2525 18.9803H16.2518C16.2405 19.1209 16.1364 19.242 15.991 19.2693C15.722 19.3205 14.5522 19.3579 13.3468 19.3691L13.347 19.3695Z" fill="#887DFF" />
  <path fillRule="evenodd" clipRule="evenodd" d="M27.7266 18.1893C26.7459 18.1985 25.73 18.1883 25.2948 18.1504C25.1625 18.148 25.0462 18.052 25.0232 17.9167C24.2129 13.1852 21.262 12.7772 17.9875 12.3245C17.5458 12.2634 17.0983 12.2015 16.6405 12.1273C16.4964 12.115 16.3833 11.9941 16.3833 11.8469V7.94009H16.3845C16.3845 7.7853 16.5101 7.65971 16.6649 7.65971C17.4912 7.65673 18.415 7.59283 19.3024 7.41999C20.0136 7.28147 20.6971 7.0731 21.2799 6.77047C21.2892 6.76511 21.2988 6.76015 21.3089 6.75578C24.3528 5.43097 24.63 3.08119 24.7279 2.2511C24.7549 2.0221 24.7721 1.87546 24.8644 1.77922C24.885 1.75818 24.9093 1.73971 24.937 1.72523C25.0759 1.6524 28.1201 1.6661 29.4745 1.67205C29.6138 1.67264 29.7303 1.67321 30.0547 1.67321C30.1825 1.67341 30.2984 1.76114 30.3286 1.89092C30.6562 3.29431 29.4691 6.08999 27.9383 7.84602C27.472 8.38103 26.9709 8.82517 26.4674 9.11172C25.9334 9.41574 25.3927 9.54572 24.8781 9.42923C24.3564 9.31116 23.8799 8.94519 23.4898 8.25659L23.2187 9.26172C23.1963 9.34407 23.137 9.41553 23.0514 9.44966L21.9604 9.88604L23.0322 10.3115C23.1171 10.3417 23.1862 10.4119 23.2104 10.5056L23.4628 11.4734C23.5539 11.225 23.6422 11.0061 23.743 10.8168C24.1933 9.97218 24.7688 9.79513 26.3329 10.6278L26.3418 10.6324C27.5148 11.271 28.507 12.2564 29.2034 13.4161C29.8942 14.5665 30.2954 15.8898 30.2954 17.2177C30.2954 17.3092 30.2929 17.4146 30.2879 17.5333C30.2831 17.6511 30.2766 17.7563 30.2688 17.8488H30.2682C30.2583 17.9718 30.1672 18.0778 30.04 18.1016C29.8047 18.1465 28.7811 18.1792 27.7264 18.189L27.7266 18.1893Z" fill="#887DFF" />
  <path fillRule="evenodd" clipRule="evenodd" d="M40.1061 17.0097C39.2655 17.0175 38.3948 17.0088 38.0218 16.9763C37.9083 16.9743 37.8086 16.892 37.7889 16.776C37.0944 12.7204 34.5651 12.3707 31.7583 11.9827C31.3797 11.9303 30.9962 11.8772 30.6037 11.8136C30.4803 11.8031 30.3833 11.6995 30.3833 11.5733V8.2246H30.3843C30.3843 8.09192 30.492 7.98427 30.6247 7.98427C31.3329 7.98172 32.1247 7.92695 32.8854 7.7788C33.495 7.66007 34.0808 7.48146 34.5804 7.22207C34.5884 7.21747 34.5966 7.21322 34.6052 7.20948C37.2143 6.07392 37.4519 4.05983 37.5358 3.34832C37.5589 3.15203 37.5737 3.02634 37.6528 2.94385C37.6705 2.92582 37.6913 2.90999 37.7151 2.89757C37.8341 2.83515 40.4434 2.84689 41.6043 2.85199C41.7237 2.8525 41.8236 2.85299 42.1017 2.85299C42.2112 2.85316 42.3105 2.92835 42.3364 3.03959C42.6172 4.2425 41.5997 6.6388 40.2876 8.14397C39.8879 8.60254 39.4584 8.98324 39.0268 9.22886C38.5691 9.48944 38.1056 9.60085 37.6646 9.50101C37.2174 9.3998 36.809 9.08611 36.4746 8.49589L36.2422 9.35742C36.223 9.42801 36.1721 9.48926 36.0988 9.51851L35.1636 9.89256L36.0823 10.2572C36.1551 10.2831 36.2143 10.3433 36.2351 10.4236L36.4514 11.2532C36.5295 11.0402 36.6052 10.8526 36.6916 10.6903C37.0776 9.96639 37.5708 9.81464 38.9115 10.5284L38.9192 10.5323C39.9246 11.0796 40.7751 11.9243 41.3719 12.9184C41.964 13.9044 42.308 15.0386 42.308 16.1769C42.308 16.2553 42.3058 16.3456 42.3015 16.4473C42.2974 16.5484 42.2918 16.6385 42.2852 16.7178H42.2847C42.2762 16.8232 42.1981 16.9141 42.0891 16.9345C41.8873 16.9729 41.01 17.001 40.1059 17.0093L40.1061 17.0097Z" fill="#887DFF" />
</svg>
);




// from header 
//Hamburger
export const Hamburger = () => (
  <svg width="20" height="20" fill="none" stroke="white" strokeWidth="2" viewBox="0 0 24 24">
    <path d="M3 12h18M3 6h18M3 18h18" />
  </svg>);

//credit icon
export const Crediticon = () => (
  <svg width="12" height="16" viewBox="0 0 12 16" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path fillRule="evenodd" clipRule="evenodd" d="M9.7228 15.0795C8.88219 15.0873 8.01147 15.0786 7.63846 15.0461C7.525 15.0441 7.42532 14.9618 7.40559 14.8458C6.71109 10.7902 4.18178 10.4405 1.37504 10.0525C0.996413 10.0001 0.612851 9.94704 0.220442 9.88342C0.0969534 9.87288 0 9.76931 0 9.6431V6.29442H0.00102987C0.00102987 6.16175 0.108698 6.0541 0.241372 6.0541C0.949647 6.05154 1.74144 5.99677 2.50211 5.84862C3.11173 5.72989 3.69752 5.55129 4.19709 5.29189C4.20509 5.2873 4.21325 5.28304 4.22192 5.2793C6.83101 4.14375 7.06863 2.12965 7.15249 1.41815C7.17562 1.22186 7.19042 1.09617 7.26951 1.01367C7.2872 0.995642 7.30796 0.979812 7.33177 0.967395C7.45084 0.90497 10.0601 0.916712 11.221 0.921815C11.3404 0.922325 11.4403 0.922811 11.7184 0.922811C11.8279 0.922981 11.9272 0.998175 11.9531 1.10942C12.2339 2.31233 11.2164 4.70862 9.90429 6.21379C9.50457 6.67237 9.07508 7.05306 8.64355 7.29868C8.18582 7.55926 7.72231 7.67068 7.28126 7.57083C6.83408 7.46962 6.42568 7.15594 6.09127 6.56571L5.85892 7.42725C5.8397 7.49784 5.78885 7.55908 5.71554 7.58834L4.78035 7.96238L5.69903 8.32705C5.77183 8.35291 5.83103 8.41311 5.85178 8.4934L6.06814 9.32297C6.14621 9.11001 6.2219 8.9224 6.30831 8.76013C6.69426 8.03621 7.18753 7.88446 8.52822 8.59818L8.53587 8.6021C9.5413 9.14946 10.3918 9.99414 10.9886 10.9882C11.5807 11.9742 11.9247 13.1084 11.9247 14.2467C11.9247 14.3251 11.9225 14.4154 11.9182 14.5171C11.9141 14.6182 11.9085 14.7083 11.9019 14.7876H11.9014C11.8929 14.8931 11.8148 14.9839 11.7058 15.0043C11.504 15.0428 10.6267 15.0708 9.72263 15.0792L9.7228 15.0795Z" fill="#887DFF" />
  </svg>);

//notification icon

export const NotificationIcon = () => (
  <svg width="18" height="18" className="sm:w-5 sm:h-5" fill="none" stroke="#9ca3af" strokeWidth="1.5" viewBox="0 0 24 24" >
    <path d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V4a2 2 0 10-4 0v1.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9" />
  </svg >);

//modals icons
export const ImagetoImageModalIcon = () => (
  <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    <rect x="3" y="5" width="14" height="10" rx="2" stroke="#fff" strokeWidth="1.5" />
    <circle cx="7" cy="10" r="1.5" fill="#fff" />
    <path d="M13 8L16 12" stroke="#fff" strokeWidth="1.5" strokeLinecap="round" />
    <path d="M10 8L13 12" stroke="#fff" strokeWidth="1.5" strokeLinecap="round" />
  </svg>);

export const ImagetoVideoModalIcon = () => (
  <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg" >
    <rect x="3" y="5" width="14" height="10" rx="2" stroke="#fff" strokeWidth="1.5" />
    <circle cx="7" cy="10" r="1.5" fill="#fff" />
    <path d="M13 8L16 12" stroke="#fff" strokeWidth="1.5" strokeLinecap="round" />
    <path d="M10 8L13 12" stroke="#fff" strokeWidth="1.5" strokeLinecap="round" />
  </svg >);


//Social posts


