import { NextRequest, NextResponse } from "next/server";
import { connectToDatabase } from "../../../../lib/mongodb";
import jwt from "jsonwebtoken";
import { OAuth2Client } from "google-auth-library";

const JWT_SECRET = process.env.JWT_SECRET!;
const GOOGLE_CLIENT_ID = process.env.GOOGLE_CLIENT_ID!;
const client = new OAuth2Client(GOOGLE_CLIENT_ID);

export async function POST(req: NextRequest) {
  try {
    const { token } = await req.json();

    if (!token) {
      return NextResponse.json(
        { error: "Token is required" },
        { status: 400 }
      );
    }

    // Set the access token
    client.setCredentials({ access_token: token });

    // Get user info using the access token
    const userInfoResponse = await fetch(
      'https://www.googleapis.com/oauth2/v2/userinfo',
      {
        headers: { Authorization: `Bear<PERSON> ${token}` }
      }
    );

    if (!userInfoResponse.ok) {
      return NextResponse.json(
        { error: "Failed to fetch user info from Google" },
        { status: 401 }
      );
    }

    const userInfo = await userInfoResponse.json();
    const { email, name, picture } = userInfo;

    if (!email) {
      return NextResponse.json(
        { error: "Email not provided by Google" },
        { status: 400 }
      );
    }

    const { db } = await connectToDatabase();
    const users = db.collection("users");

    // Find or create user
    let user = await users.findOne({ email });
    if (!user) {
      const result = await users.insertOne({
        email,
        name: name || email.split('@')[0],
        picture,
        emailVerified: true,
        provider: 'google',
        createdAt: new Date(),
      });
      user = await users.findOne({ _id: result.insertedId });
    }

    const jwtToken = jwt.sign(
      {
        email: user.email,
        userId: user._id,
        name: user.name,
      },
      JWT_SECRET,
      { expiresIn: "7d" }
    );

    const response = NextResponse.json({
      ok: true,
      user: {
        name: user.name,
        email: user.email,
        emailVerified: user.emailVerified,
        picture: user.picture,
      },
    });

    response.cookies.set("token", jwtToken, {
      httpOnly: true,
      path: "/",
      maxAge: 60 * 60 * 24 * 7,
      secure: process.env.NODE_ENV === "production",
      sameSite: "lax",
    });

    return response;
  } catch (error) {
    console.error("Google auth error:", error);
    return NextResponse.json(
      { error: "Error during Google authentication" },
      { status: 500 }
    );
  }
} 