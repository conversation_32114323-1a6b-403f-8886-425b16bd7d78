{"dependencies": {"@auth/mongodb-adapter": "^3.9.1", "@react-oauth/google": "^0.12.2", "@types/bcryptjs": "^2.4.6", "@types/jsonwebtoken": "^9.0.9", "@types/next": "^8.0.7", "@types/next-auth": "^3.13.0", "@types/node": "^22.15.29", "@types/nodemailer": "^6.4.17", "@types/react": "^19.1.6", "apple-signin-auth": "^2.0.0", "bcryptjs": "^3.0.2", "cloudinary": "^2.4.0", "geist": "^1.4.2", "google-auth-library": "^9.15.1", "jsonwebtoken": "^9.0.2", "mongodb": "^6.16.0", "next-auth": "^4.24.11", "nodemailer": "^6.10.1", "react-apple-signin-auth": "^1.1.2", "replicate": "^1.0.1"}, "scripts": {"dev": "next dev", "build": "next build", "start": "next start"}, "devDependencies": {"autoprefixer": "^10.4.16", "postcss": "^8.4.31", "tailwindcss": "^3.3.5", "typescript": "5.8.3"}, "name": "keoo-ai", "version": "1.0.0", "description": "", "main": "index.js", "repository": {"type": "git", "url": "git+https://github.com/Bluevangas/keoo-ai.git"}, "keywords": [], "author": "", "license": "ISC", "bugs": {"url": "https://github.com/Bluevangas/keoo-ai/issues"}, "homepage": "https://github.com/Bluevangas/keoo-ai#readme"}