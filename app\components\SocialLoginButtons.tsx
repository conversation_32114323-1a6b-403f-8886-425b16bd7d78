import { useGoogleLogin } from '@react-oauth/google';
import { useRouter } from 'next/navigation';
import AppleSignInInit from './AppleSignInInit';
import { AppleIcon, Googleicon } from './icons/icons';

export default function SocialLoginButtons() {
  const router = useRouter();

  const googleLogin = useGoogleLogin({
    onSuccess: async (response) => {
      try {
        const res = await fetch('/api/auth/google', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ token: response.access_token }),
        });

        if (res.ok) {
          router.push('/dashboard');
        } else {
          const data = await res.json();
          console.error('Google login failed:', data.error);
        }
      } catch (error) {
        console.error('Google login error:', error);
      }
    },
  });

  const handleAppleSuccess = async (response: any) => {
    try {
      const res = await fetch('/api/auth/apple', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ token: response.id_token }),
      });

      if (res.ok) {
        router.push('/dashboard');
      } else {
        const data = await res.json();
        console.error('Apple login failed:', data.error);
      }
    } catch (error) {
      console.error('Apple login error:', error);
    }
  };

  return (
    <>
      <AppleSignInInit />
      <div className="flex flex-col gap-3 w-full">
        <button
          onClick={() => googleLogin()}
          className="flex   gap-3 w-full px-10 py-5 rounded-full bg-[#030F0F] border border-[#35355a] text-white hover:bg-[#1f1f35] hover:border-[#4a4a6a] hover:shadow-lg hover:shadow-black/20 active:scale-[0.98] focus:outline-none focus:ring-2 focus:ring-[#7c5cff] transition-all duration-300 ease-in-out transform hover:scale-[1.02]"
        >
          <Googleicon />
          Continue with Google
        </button>

        <button
          onClick={() => {
            // Trigger Apple Sign In
            const appleButton = document.getElementById('appleid-signin');
            if (appleButton) {
              appleButton.click();
            }
          }}
          className="flex  gap-3 w-full px-10 py-5 rounded-full bg-[#030F0F] border border-[#35355a] text-[#F7F5F2] hover:bg-[#1f1f35] hover:border-[#4a4a6a] hover:shadow-lg hover:shadow-black/20 active:scale-[0.98] focus:outline-none focus:ring-2 focus:ring-[#7c5cff] transition-all duration-300 ease-in-out transform hover:scale-[1.02]"
        >
          <AppleIcon />
          Continue with Apple
        </button>
      </div>
    </>
  );
} 