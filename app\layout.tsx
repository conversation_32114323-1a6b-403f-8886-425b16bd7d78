import { GoogleOAuthProvider } from '@react-oauth/google';
import { GeistSans } from 'geist/font/sans';
import "./globals.css";
import Providers from "./providers";

export const metadata = {
  title: "Keoo AI",
  description: "Your AI-powered assistant",
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body className={GeistSans.className} suppressHydrationWarning>
        <Providers>
          <GoogleOAuthProvider clientId={process.env.NEXT_PUBLIC_GOOGLE_CLIENT_ID!}>
            {children}
          </GoogleOAuthProvider>
        </Providers>
      </body>
    </html>
  );
}
