"use client";
import { useState } from 'react';
import { useRouter } from 'next/navigation';
import {
  DashboardLayout,
  Header,
  Sidebar,
} from '../components';
import { useChatHistory } from '../hooks/useChatHistory';
import { Inspirationicon, IdeaLabicon, SocialPosticon, Clippingicon, Storyboardicon, Youtubeicon, Keywordicon, Accounticon } from '../components/icons/icons';

// Icon components for better consistency


const sidebarMenu = [
  { label: "Inspiration", icon: <Inspirationicon />, active: false },
  { label: "Idea Lab", icon: <IdeaLabicon />, active: false },
  { label: "Social Post", icon: <SocialPosticon />, active: false },
  { label: "Clipping", icon: <Clippingicon />, active: false },
  { label: "Storyboard Editor", icon: <Storyboardicon />, active: false },
  { label: "YouTube Tools", icon: <Youtubeicon />, active: true },
  { label: "Keyword Insights", icon: <Keywordicon />, active: false },
  { label: "Account", icon: <Accounticon />, active: false },
];

export default function YouTubeTools() {
  const router = useRouter();

  // Chat history management for sidebar
  const {
    historySections,
    isLoading,
    clearAllHistory
  } = useChatHistory();

  // Handle menu item clicks with navigation
  const handleMenuItemClick = (item: any, index: number) => {
    const routes: { [key: string]: string } = {
      "Inspiration": "/dashboard",
      "Idea Lab": "/idealab",
      "Social Post": "/social-post",
      "Clipping": "/clipping",
      "Storyboard Editor": "/storyboard",
      "YouTube Tools": "/youtube-tools",
      "Keyword Insights": "/keyword-insights",
      "Account": "/account"
    };

    const route = routes[item.label];
    if (route) {
      router.push(route);
    }
  };

  // Handle history item click to navigate to Idea Lab
  const handleHistoryItemClick = (historyItem: any, sectionIndex: number, itemIndex: number) => {
    if (historyItem.sessionId) {
      // Navigate to Idea Lab with the session
      router.push('/idealab');
    }
  };

  // Handle clear history
  const handleClearHistory = () => {
    clearAllHistory();
  };

  return (
    <DashboardLayout
      header={<Header />}
      sidebar={
        <Sidebar
          menuItems={sidebarMenu}
          histories={historySections}
          isLoading={isLoading}
          onMenuItemClick={handleMenuItemClick}
          onHistoryItemClick={handleHistoryItemClick}
          onClearHistory={handleClearHistory}
        />
      }
    >
      <div className="flex-1 p-6 overflow-auto">
        <div className="max-w-4xl mx-auto">
          <div className="mb-6">
            <h1 className="text-2xl font-semibold text-white mb-2">YouTube Tools</h1>
            <p className="text-gray-400 text-sm">Enhance your YouTube content</p>
          </div>

          <div className="bg-[#1a1a2e] border border-[#2a2a3e] rounded-lg p-8 text-center">
            <div className="w-16 h-16 bg-[#2a2a3e] rounded-lg flex items-center justify-center mx-auto mb-4">
              {<Youtubeicon />}
            </div>
            <h3 className="text-xl font-semibold text-white mb-2">YouTube Tools</h3>
            <p className="text-gray-400">Coming soon! Enhance your YouTube content with powerful tools.</p>
          </div>
        </div>
      </div>
    </DashboardLayout>
  );
}
